import { Link } from '@heroui/react';
export default function FeaturedOn() {
  return (
    <div className='flex items-center flex-wrap gap-1'>
      <Link href="https://aistage.net" className="text-sm hover:text-primary" target="_blank">AIStage</Link>
      <span className="mx-2">|</span>
      <Link href="https://startupfa.me/s/black-screen?utm_source=black-screen.cc" target="_blank">
        <img src="https://startupfa.me/badges/featured-badge-small.webp" alt="Featured on Startup Fame" className='h-[36px] w-auto' />
      </Link>
      <span className="mx-2">|</span>
      <a href="https://turbo0.com/item/black-screen" target="_blank" rel="noopener noreferrer">
        <img src="https://img.turbo0.com/badge-listed-light.svg" alt="Listed on Turbo0" className='h-[36px] w-auto' />
      </a>
      <span className="mx-2">|</span>
      <a href="https://submitaitools.org" target="_blank" >
        <img src="https://submitaitools.org/static_submitaitools/images/submitaitools.png" alt="Submit AI Tools – The ultimate platform to discover, submit, and explore the best AI tools across various categories." className='h-[36px] w-auto' />
      </a>
      <span className="mx-2">|</span>
      <a href="https://twelve.tools" target="_blank"><img src="https://twelve.tools/badge0-white.svg" alt="Featured on Twelve Tools" className='h-[36px] w-auto' /></a>
      <span className="mx-2">|</span>
      <a title="ai tools code.market" href="https://code.market?code.market=verified"><img alt="ai tools code.market" title="ai tools code.market" src="https://code.market/assets/manage-product/featured-logo-bright.svg" className='h-[36px] w-auto' /></a>
      <span className="mx-2">|</span>
      <a href="https://kontext-ai.com/">Kontext AI</a>
      <span className="mx-2">|</span>
      <a href="https://goodaitools.com"><img src="https://goodaitools.com/assets/images/badge.png" alt="Good AI Tools" className='h-[36px] w-auto' /></a>
    </div>
  )
}
