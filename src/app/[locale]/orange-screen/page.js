import { getTranslation } from '@/lib/i18n';
import Hero from '@/app/components/ui/Hero';
import { generateAlternates } from "@/lib/hreflang";


export async function generateMetadata({ params }) {
  const title = 'Orange Screen for Fullscreen Testing & Calibration';
  const description = 'Free orange screen for display testing and color calibration in fullscreen mode. Ensure accurate orange display and download high-resolution images.';
  const image = `${process.env.NEXT_PUBLIC_SITE_URL}/images/orange-og.png`;
  const currentLocale = params.locale || 'en';

  return {
    title: title,
    description: description,
    alternates: generateAlternates('/orange-screen', currentLocale),
    openGraph: {
      title: title,
      description: description,
      type: 'website',
      url: `${process.env.NEXT_PUBLIC_SITE_URL}`,
      siteName: 'Black Screen',
      images: [{
        url: image
      }]
    },
    twitter: {
      card: 'summary_large_image',
      site: '@Black Screen',
      title: title,
      description: description,
      images: [image]
    }
  }
}


export default function BlueScreenPage({ params }) {
  const locale = params.locale || 'en';

  const t = function (key) {
    return getTranslation(locale, key);
  };

  return (
    <div className="page-container py-8">
      <div className="section">
        <Hero locale={locale} color='orange' />
      </div>
      <div className="section bg-gray-100 dark:bg-gray-800 rounded mt-20">
        <h2 className="text-2xl font-bold px-2 py-4">{t("Discovering the Vibrancy of Orange Screen Tool")}</h2>
        <div className="px-2">
          <p>
            {t("The orange screen is a standout element of the orange screen tool, offering a free, warm fullscreen display designed for display testing and color calibration. As a key feature of the Orange Screen suite, this tool leverages a rich orange backdrop to evaluate color accuracy, detect display anomalies, and ensure vibrant screen performance. Distinct from other shades, the orange screen is perfect for assessing warm tones, monitor alignment, and screen health maintenance. Accessible online without registration, the orange screen tool also provides high-resolution image downloads, appealing to designers, tech users, and creative professionals. Its full screen mode delivers an immersive experience, establishing it as a top solution for color calibration and visual enhancement.")}
          </p>
        </div>
      </div>
      <div id="use-case" className='section bg-gray-100 dark:bg-gray-800 rounded mt-20'>
        <h2 className="text-2xl font-bold px-2 py-4">{t("Broad Use Cases for Orange Screen Tool")}</h2>
        <div className="px-2">
          <p className='mb-4'>{t("Uncover the extensive applications of the orange screen through ten varied use cases, highlighting the orange screen tool's flexibility in full screen mode. Drawing inspiration from practical needs and competitor insights, these scenarios demonstrate its wide-ranging utility.")}</p>
          <ul className='list-disc pl-6 space-y-2 mb-4'>
            <li><strong>{t("Color Calibration Precision")}</strong>: {t("Use the orange screen in full screen mode to fine-tune warm color tones, essential for photographers and graphic designers calibrating displays.")}</li>
            <li><strong>{t("Display Quality Assessment")}</strong>: {t("Employ the orange screen tool's orange screen to spot color inaccuracies or dead pixels, a critical task for IT professionals and display experts.")}</li>
            <li><strong>{t("Screen Cleaning Enhancement")}</strong>: {t("Illuminate dust and smudges with the orange screen's bright full screen display, ideal for maintaining monitors or large screens effectively.")}</li>
            <li><strong>{t("Creative Design Validation")}</strong>: {t("Designers can leverage the orange screen in full screen to verify orange hues in artwork, ensuring consistency across creative projects.")}</li>
            <li><strong>{t("Gaming Ambiance Boost")}</strong>: {t("Gamers can activate the orange screen tool's orange screen to adjust monitor settings, enhancing warm tones for an immersive gaming experience.")}</li>
            <li><strong>{t("Presentation Style Upgrade")}</strong>: {t("Download orange screen images from the orange screen tool for vibrant, eye-catching slide backgrounds in professional or educational settings.")}</li>
            <li><strong>{t("Eye Comfort During Breaks")}</strong>: {t("Switch to the orange screen in full screen mode to reduce eye strain, offering a soothing contrast for extended screen time.")}</li>
            <li><strong>{t("Device Color Diagnostics")}</strong>: {t("Tech enthusiasts can use the orange screen tool's full screen orange display to diagnose screen burn-in or color balance issues.")}</li>
            <li><strong>{t("Educational Color Exploration")}</strong>: {t("Teachers can project the orange screen in full screen mode to teach color theory, supported by the tool's free access.")}</li>
            <li><strong>{t("Retail Display Refinement")}</strong>: {t("Retailers can apply the orange screen tool to test and align large display screens, ensuring striking orange outputs for product presentations.")}</li>
          </ul>
          <p className='mt-4'>{t("These use cases highlight the orange screen's versatility, positioning the orange screen tool as a valuable asset for creative, technical, and educational pursuits, enhanced by its full screen capabilities.")}</p>
        </div>
      </div>
    </div>
  );
}
